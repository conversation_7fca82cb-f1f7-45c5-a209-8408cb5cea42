{"name": "aquisitions-automation", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@auth/mongodb-adapter": "^3.4.2", "@hookform/resolvers": "^3.3.2", "@tanstack/react-query": "^5.17.0", "@types/puppeteer": "^5.4.7", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^3.0.6", "framer-motion": "^11.11.17", "lucide-react": "^0.460.0", "mongodb": "^6.3.0", "mongoose": "^8.0.3", "next": "^14.2.30", "next-auth": "^4.24.7", "next-themes": "^0.4.6", "openai": "^4.67.3", "puppeteer": "^23.10.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.14", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8.4.24", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "tailwindcss": "^3.4.0", "typescript": "^5"}}