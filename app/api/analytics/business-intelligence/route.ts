import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { BusinessIntelligenceService } from '@/lib/analytics/business-intelligence';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin privileges (in a real app, this would check user roles)
    const isAdmin = session.user?.email === '<EMAIL>';
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required for business intelligence data' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const format = searchParams.get('format') || 'json';

    // Default to last 30 days if no dates provided
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    const biService = new BusinessIntelligenceService();
    const report = biService.generateBIReport(start, end);

    if (format !== 'json') {
      const exportedData = biService.exportBIReport(report, format as 'csv' | 'excel');
      
      return NextResponse.json({
        success: true,
        data: {
          report,
          exportData: exportedData,
          format
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: report,
    });

  } catch (error) {
    console.error('Business intelligence error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const isAdmin = session.user?.email === '<EMAIL>';
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { 
      reportType = 'comprehensive',
      dateRange,
      includeForecasts = true,
      includeGeographic = true,
      customMetrics = []
    } = body;

    const startDate = dateRange?.start ? new Date(dateRange.start) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const endDate = dateRange?.end ? new Date(dateRange.end) : new Date();

    const biService = new BusinessIntelligenceService();
    let report = biService.generateBIReport(startDate, endDate);

    // Customize report based on request
    if (!includeForecasts) {
      report.predictiveInsights = {
        marketForecast: [],
        opportunityZones: [],
        riskAlerts: []
      };
    }

    if (!includeGeographic) {
      report.geographicInsights = {
        topPerformingZones: [],
        transitCorridors: [],
        riskAreas: []
      };
    }

    // Add custom metrics if provided
    if (customMetrics.length > 0) {
      const customData = customMetrics.reduce((acc: any, metric: any) => {
        acc[metric.name] = metric.value;
        return acc;
      }, {});

      report = {
        ...report,
        customMetrics: customData
      };
    }

    return NextResponse.json({
      success: true,
      data: report,
    });

  } catch (error) {
    console.error('Custom BI report error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
