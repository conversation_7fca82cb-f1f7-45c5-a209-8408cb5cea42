'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  MapPin, 
  Building, 
  DollarSign, 
  Ruler, 
  FileText,
  Download,
  Share,
  Star,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react';
import toast from 'react-hot-toast';

interface PropertyDetailsProps {
  params: {
    id: string;
  };
}

export default function PropertyDetailsPage({ params }: PropertyDetailsProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [property, setProperty] = useState<any>(null);
  const [evaluation, setEvaluation] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [evaluating, setEvaluating] = useState(false);
  const [generatingReport, setGeneratingReport] = useState(false);

  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    fetchPropertyDetails();
  }, [session, status, params.id]);

  const fetchPropertyDetails = async () => {
    try {
      const response = await fetch(`/api/properties/evaluate?propertyId=${params.id}`);
      const data = await response.json();

      if (data.success) {
        setProperty(data.data.property);
        if (data.data.property.evaluationResults) {
          setEvaluation(data.data.property.evaluationResults);
        }
      } else {
        toast.error('Failed to load property details');
      }
    } catch (error) {
      toast.error('Error loading property details');
    } finally {
      setLoading(false);
    }
  };

  const handleEvaluateProperty = async () => {
    setEvaluating(true);
    try {
      const response = await fetch('/api/properties/evaluate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          propertyId: params.id,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setEvaluation(data.data.evaluation);
        toast.success('Property evaluation completed');
      } else {
        toast.error('Failed to evaluate property');
      }
    } catch (error) {
      toast.error('Error evaluating property');
    } finally {
      setEvaluating(false);
    }
  };

  const handleGenerateReport = async () => {
    setGeneratingReport(true);
    try {
      const response = await fetch('/api/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          propertyId: params.id,
          reportType: 'detailed-analysis',
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Report generated successfully');
        // Could redirect to report view or download
      } else {
        toast.error('Failed to generate report');
      }
    } catch (error) {
      toast.error('Error generating report');
    } finally {
      setGeneratingReport(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!property) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Property Not Found
          </h1>
          <Button onClick={() => router.back()}>Go Back</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {property.address}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                Parcel ID: {property.parcelId || 'N/A'}
              </p>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" size="sm">
                <Share className="w-4 h-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" size="sm">
                <Star className="w-4 h-4 mr-2" />
                Save
              </Button>
            </div>
          </div>
        </div>

        {/* Evaluation Status */}
        {evaluation && (
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-full ${
                    evaluation.passed 
                      ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400'
                      : 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400'
                  }`}>
                    {evaluation.passed ? (
                      <CheckCircle className="w-6 h-6" />
                    ) : (
                      <XCircle className="w-6 h-6" />
                    )}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {evaluation.passed ? 'Qualified Property' : 'Not Qualified'}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Overall Score: {evaluation.overallScore}/100
                    </p>
                  </div>
                </div>
                <div className="flex space-x-3">
                  <Button 
                    onClick={handleGenerateReport}
                    disabled={generatingReport}
                  >
                    {generatingReport ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <FileText className="w-4 h-4 mr-2" />
                    )}
                    Generate Report
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Property Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Property Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3">
                    <Ruler className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Lot Size</p>
                      <p className="font-medium">{formatNumber(property.lotSize)} sq ft</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Building className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Zoning</p>
                      <p className="font-medium">{property.zoning}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <DollarSign className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Assessed Value</p>
                      <p className="font-medium">{formatCurrency(property.assessedValue)}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <DollarSign className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Annual Taxes</p>
                      <p className="font-medium">{formatCurrency(property.taxAmount)}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Owner Information */}
            <Card>
              <CardHeader>
                <CardTitle>Owner Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Name</p>
                    <p className="font-medium">{property.owner.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Address</p>
                    <p className="font-medium">{property.owner.address}</p>
                  </div>
                  {property.owner.phone && (
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Phone</p>
                      <p className="font-medium">{property.owner.phone}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Evaluation Results */}
            {evaluation && (
              <Card>
                <CardHeader>
                  <CardTitle>Evaluation Results</CardTitle>
                  <CardDescription>
                    Detailed analysis of development criteria
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Criteria Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <CriteriaItem
                        label="QCT/DDA Status"
                        passed={evaluation.criteria.qctDdaStatus}
                        description="Qualified Census Tract or Difficult Development Area"
                      />
                      <CriteriaItem
                        label="Neighborhood Change Zone"
                        passed={evaluation.criteria.neighborhoodChangeZone}
                        description="Located in designated change zone"
                      />
                      <CriteriaItem
                        label="Lot Size"
                        passed={evaluation.criteria.lotSizeAnalysis.isIdeal || evaluation.criteria.lotSizeAnalysis.isAcceptable}
                        description={`${formatNumber(evaluation.criteria.lotSizeAnalysis.size)} sq ft`}
                      />
                      <CriteriaItem
                        label="Density Potential"
                        passed={evaluation.criteria.densityPotential.meets250Requirement}
                        description={`${evaluation.criteria.densityPotential.totalUnits} total units`}
                      />
                      <CriteriaItem
                        label="Height Restrictions"
                        passed={evaluation.criteria.heightRestrictions.meets65FtRequirement}
                        description={`${evaluation.criteria.heightRestrictions.baseHeight} ft base height`}
                      />
                      <CriteriaItem
                        label="Transit Access"
                        passed={evaluation.criteria.transitAccess.isEligible}
                        description={`${evaluation.criteria.transitAccess.nearestStopDistance.toFixed(2)} miles to transit`}
                      />
                    </div>

                    {/* Recommendations */}
                    {evaluation.recommendations.length > 0 && (
                      <div className="mt-6">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                          Recommendations
                        </h4>
                        <ul className="space-y-2">
                          {evaluation.recommendations.map((rec: string, index: number) => (
                            <li key={index} className="flex items-start space-x-2">
                              <AlertTriangle className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">{rec}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {!evaluation && (
                  <Button 
                    onClick={handleEvaluateProperty}
                    disabled={evaluating}
                    className="w-full"
                  >
                    {evaluating ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <CheckCircle className="w-4 h-4 mr-2" />
                    )}
                    Evaluate Property
                  </Button>
                )}
                
                <Button variant="outline" className="w-full">
                  <MapPin className="w-4 h-4 mr-2" />
                  View on Map
                </Button>
                
                <Button variant="outline" className="w-full">
                  <Download className="w-4 h-4 mr-2" />
                  Export Data
                </Button>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            {evaluation && (
              <Card>
                <CardHeader>
                  <CardTitle>Quick Stats</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Overall Score</span>
                      <span className="font-medium">{evaluation.overallScore}/100</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Potential Units</span>
                      <span className="font-medium">{evaluation.criteria.densityPotential.totalUnits}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Confidence</span>
                      <span className="font-medium">{evaluation.confidenceScore}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

function CriteriaItem({ 
  label, 
  passed, 
  description 
}: { 
  label: string; 
  passed: boolean; 
  description: string; 
}) {
  return (
    <div className="flex items-start space-x-3 p-3 border rounded-lg">
      <div className={`p-1 rounded-full ${
        passed 
          ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400'
          : 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400'
      }`}>
        {passed ? (
          <CheckCircle className="w-4 h-4" />
        ) : (
          <XCircle className="w-4 h-4" />
        )}
      </div>
      <div className="flex-1">
        <p className="font-medium text-gray-900 dark:text-white">{label}</p>
        <p className="text-sm text-gray-600 dark:text-gray-400">{description}</p>
      </div>
    </div>
  );
}
